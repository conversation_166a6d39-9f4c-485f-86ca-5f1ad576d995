# 量化交易服务器环境配置文件示例
# 复制此文件为 .env 并根据实际情况修改配置

# 应用基本配置
APP_NAME=量化交易服务器
VERSION=1.0.0
DEBUG=true

# 服务器配置
HOST=0.0.0.0
PORT=8000

# API配置
API_PREFIX=/api/v1

# 日志配置
LOG_LEVEL=INFO

# Qlib配置
QLIB_PROVIDER=yahoo
QLIB_REGION=cn
QLIB_MARKET=csi300

# 数据源配置
DATA_SOURCE=efinance
DATA_CACHE_EXPIRE=3600

# 模型配置
MODEL_CACHE_SIZE=100
MODEL_RETRAIN_INTERVAL=86400

# 回测配置
BACKTEST_CACHE_SIZE=50
BACKTEST_MAX_PERIOD=365

# 安全配置
API_RATE_LIMIT=100
API_RATE_LIMIT_PERIOD=60

# 数据库配置（如果需要）
# DATABASE_URL=sqlite:///./quantify.db
# REDIS_URL=redis://localhost:6379/0