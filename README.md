# Quantify Server

量化交易股票数据服务 - 基于FastAPI的股票数据API服务

## 项目简介

Quantify Server 是一个基于 FastAPI 框架开发的股票数据服务，主要提供股票基本信息查询和历史K线数据获取功能。项目使用 efinance 库作为数据源，提供RESTful风格的API接口。

## 主要功能

- 📊 **股票基本信息查询**: 获取股票的基本信息，包括市盈率、市净率、总股本等
- 📈 **历史K线数据**: 支持日K、周K、月K线数据查询
- 🚀 **高性能**: 基于FastAPI框架，支持异步处理
- 📝 **完整文档**: 自动生成的API文档，支持在线测试
- 🛡️ **错误处理**: 完善的异常处理和日志记录机制

## 技术栈

- **Python 3.8+**
- **FastAPI**: 现代、快速的Web框架
- **Uvicorn**: ASGI服务器
- **efinance**: 股票数据获取库
- **Pydantic**: 数据验证和序列化
- **Conda**: 环境管理

## 项目结构

```
quantify-server/
├── app/                    # 应用主目录
│   ├── api/               # API路由
│   │   ├── __init__.py
│   │   └── stock.py       # 股票相关API
│   ├── core/              # 核心配置
│   │   ├── __init__.py
│   │   ├── config.py      # 应用配置
│   │   ├── exceptions.py  # 自定义异常
│   │   └── logging.py     # 日志配置
│   ├── models/            # 数据模型
│   │   ├── __init__.py
│   │   ├── response.py    # 响应模型
│   │   └── stock.py       # 股票数据模型
│   ├── services/          # 业务服务
│   │   ├── __init__.py
│   │   └── stock_service.py # 股票数据服务
│   ├── __init__.py
│   └── main.py            # FastAPI应用实例
├── docs/                  # 文档目录
│   └── prd.md            # 产品需求文档
├── .env                   # 环境配置文件
├── main.py               # 应用入口
├── requirements.txt      # Python依赖
├── start.sh             # Linux/Mac启动脚本
├── start.bat            # Windows启动脚本
└── README.md            # 项目说明
```

## 快速开始

### 环境准备

1. **安装Conda**
   确保已安装 Anaconda 或 Miniconda

2. **创建Conda环境**
   ```bash
   conda create -n quantify python=3.8
   conda activate quantify
   ```

3. **克隆项目**
   ```bash
   git clone <repository-url>
   cd quantify-server
   ```

### 安装依赖

```bash
pip install -r requirements.txt
```

### 配置环境

编辑 `.env` 文件，配置服务参数：

```env
# 服务器配置
HOST=127.0.0.1
PORT=8000

# 日志配置
LOG_LEVEL=INFO
```

### 启动服务

#### 方式一：使用启动脚本（推荐）

**Linux/Mac:**
```bash
./start.sh
```

**Windows:**
```cmd
start.bat
```

#### 方式二：直接运行

```bash
python main.py
```

#### 方式三：使用uvicorn

```bash
uvicorn app.main:app --host 127.0.0.1 --port 8000 --reload
```

### 访问服务

- **API服务**: http://127.0.0.1:8000
- **API文档**: http://127.0.0.1:8000/docs
- **ReDoc文档**: http://127.0.0.1:8000/redoc

## API接口

### 1. 获取股票基本信息

```http
GET /api/v1/stocks/{stock_code}/base-info
```

**参数:**
- `stock_code`: 股票代码（如：000001）

**响应示例:**
```json
{
  "success": true,
  "message": "获取股票基本信息成功",
  "data": {
    "stock_code": "000001",
    "stock_name": "平安银行",
    "market": "深圳",
    "industry": "银行",
    "pe": 5.23,
    "pb": 0.67,
    "total_assets": 4500000000000,
    ...
  },
  "code": 200
}
```

### 2. 获取股票K线数据

```http
GET /api/v1/stocks/{stock_code}/kline
```

**参数:**
- `stock_code`: 股票代码（如：000001）
- `period`: 周期类型（daily/weekly/monthly）
- `start_date`: 开始日期（YYYYMMDD，可选）
- `end_date`: 结束日期（YYYYMMDD，可选）
- `count`: 数据条数（默认100，最大1000）

**响应示例:**
```json
{
  "success": true,
  "message": "获取股票K线数据成功",
  "data": {
    "stock_code": "000001",
    "stock_name": "平安银行",
    "period": "daily",
    "data": [
      {
        "date": "2024-01-01",
        "open": 10.50,
        "close": 10.80,
        "high": 10.90,
        "low": 10.40,
        "volume": 1000000,
        "amount": 10800000
      }
    ]
  },
  "code": 200
}
```

### 3. 健康检查

```http
GET /health
```

## 开发指南

### 添加新的API接口

1. 在 `app/models/` 中定义数据模型
2. 在 `app/services/` 中实现业务逻辑
3. 在 `app/api/` 中添加路由处理
4. 在 `app/main.py` 中注册路由

### 错误处理

项目实现了统一的错误处理机制：

- `StockNotFoundError`: 股票未找到
- `DataFetchError`: 数据获取失败
- `InvalidParameterError`: 参数错误

### 日志记录

项目使用Python标准库的logging模块，日志级别可通过环境变量配置。

## 部署说明

### 生产环境部署

1. **使用Gunicorn**
   ```bash
   pip install gunicorn
   gunicorn app.main:app -w 4 -k uvicorn.workers.UvicornWorker
   ```

2. **使用Docker**
   ```dockerfile
   FROM python:3.8-slim
   WORKDIR /app
   COPY requirements.txt .
   RUN pip install -r requirements.txt
   COPY . .
   CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000"]
   ```

### 性能优化

- 使用连接池管理数据库连接
- 实现缓存机制减少API调用
- 使用异步处理提高并发性能

## 常见问题

### Q: 如何更换数据源？

A: 修改 `app/services/stock_service.py` 中的数据获取逻辑，替换efinance库的调用。

### Q: 如何添加用户认证？

A: 可以使用FastAPI的依赖注入系统，添加JWT或OAuth2认证中间件。

### Q: 如何处理大量并发请求？

A: 建议使用Redis缓存、数据库连接池，并考虑使用负载均衡器。

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请通过以下方式联系：

- 提交 Issue
- 发送邮件
- 微信群讨论

---

**Quantify Server** - 让量化交易更简单 🚀