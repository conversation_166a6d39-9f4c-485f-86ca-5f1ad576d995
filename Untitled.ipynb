{"cells": [{"cell_type": "code", "execution_count": 2, "id": "c5ce4eda-9e02-451e-9bfd-7e7113a06d57", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>股票名称</th>\n", "      <th>股票代码</th>\n", "      <th>日期</th>\n", "      <th>开盘</th>\n", "      <th>收盘</th>\n", "      <th>最高</th>\n", "      <th>最低</th>\n", "      <th>成交量</th>\n", "      <th>成交额</th>\n", "      <th>振幅</th>\n", "      <th>涨跌幅</th>\n", "      <th>涨跌额</th>\n", "      <th>换手率</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>贵州茅台</td>\n", "      <td>600519</td>\n", "      <td>2025-07-15 10:30</td>\n", "      <td>1420.98</td>\n", "      <td>1416.56</td>\n", "      <td>1422.93</td>\n", "      <td>1412.02</td>\n", "      <td>13377</td>\n", "      <td>1.896731e+09</td>\n", "      <td>0.77</td>\n", "      <td>-0.49</td>\n", "      <td>-7.04</td>\n", "      <td>0.11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>贵州茅台</td>\n", "      <td>600519</td>\n", "      <td>2025-07-15 11:30</td>\n", "      <td>1416.57</td>\n", "      <td>1410.00</td>\n", "      <td>1417.98</td>\n", "      <td>1409.88</td>\n", "      <td>8459</td>\n", "      <td>1.194497e+09</td>\n", "      <td>0.57</td>\n", "      <td>-0.46</td>\n", "      <td>-6.56</td>\n", "      <td>0.07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>贵州茅台</td>\n", "      <td>600519</td>\n", "      <td>2025-07-15 14:00</td>\n", "      <td>1409.99</td>\n", "      <td>1408.35</td>\n", "      <td>1410.96</td>\n", "      <td>1408.32</td>\n", "      <td>7031</td>\n", "      <td>9.909633e+08</td>\n", "      <td>0.19</td>\n", "      <td>-0.12</td>\n", "      <td>-1.65</td>\n", "      <td>0.06</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>贵州茅台</td>\n", "      <td>600519</td>\n", "      <td>2025-07-15 15:00</td>\n", "      <td>1408.38</td>\n", "      <td>1411.00</td>\n", "      <td>1414.73</td>\n", "      <td>1408.37</td>\n", "      <td>6538</td>\n", "      <td>9.229586e+08</td>\n", "      <td>0.45</td>\n", "      <td>0.19</td>\n", "      <td>2.65</td>\n", "      <td>0.05</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>贵州茅台</td>\n", "      <td>600519</td>\n", "      <td>2025-07-16 10:30</td>\n", "      <td>1410.01</td>\n", "      <td>1412.50</td>\n", "      <td>1417.49</td>\n", "      <td>1409.95</td>\n", "      <td>8441</td>\n", "      <td>1.193176e+09</td>\n", "      <td>0.53</td>\n", "      <td>0.11</td>\n", "      <td>1.50</td>\n", "      <td>0.07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>123</th>\n", "      <td>贵州茅台</td>\n", "      <td>600519</td>\n", "      <td>2025-08-26 15:00</td>\n", "      <td>1488.00</td>\n", "      <td>1481.61</td>\n", "      <td>1488.19</td>\n", "      <td>1481.28</td>\n", "      <td>10211</td>\n", "      <td>1.515541e+09</td>\n", "      <td>0.46</td>\n", "      <td>-0.46</td>\n", "      <td>-6.86</td>\n", "      <td>0.08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>124</th>\n", "      <td>贵州茅台</td>\n", "      <td>600519</td>\n", "      <td>2025-08-27 10:30</td>\n", "      <td>1481.88</td>\n", "      <td>1463.10</td>\n", "      <td>1484.93</td>\n", "      <td>1462.00</td>\n", "      <td>20519</td>\n", "      <td>3.013180e+09</td>\n", "      <td>1.55</td>\n", "      <td>-1.25</td>\n", "      <td>-18.51</td>\n", "      <td>0.16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>125</th>\n", "      <td>贵州茅台</td>\n", "      <td>600519</td>\n", "      <td>2025-08-27 11:30</td>\n", "      <td>1463.09</td>\n", "      <td>1460.32</td>\n", "      <td>1465.33</td>\n", "      <td>1460.00</td>\n", "      <td>9728</td>\n", "      <td>1.421546e+09</td>\n", "      <td>0.36</td>\n", "      <td>-0.19</td>\n", "      <td>-2.78</td>\n", "      <td>0.08</td>\n", "    </tr>\n", "    <tr>\n", "      <th>126</th>\n", "      <td>贵州茅台</td>\n", "      <td>600519</td>\n", "      <td>2025-08-27 14:00</td>\n", "      <td>1460.32</td>\n", "      <td>1457.31</td>\n", "      <td>1468.08</td>\n", "      <td>1456.74</td>\n", "      <td>9368</td>\n", "      <td>1.368518e+09</td>\n", "      <td>0.78</td>\n", "      <td>-0.21</td>\n", "      <td>-3.01</td>\n", "      <td>0.07</td>\n", "    </tr>\n", "    <tr>\n", "      <th>127</th>\n", "      <td>贵州茅台</td>\n", "      <td>600519</td>\n", "      <td>2025-08-27 15:00</td>\n", "      <td>1457.99</td>\n", "      <td>1448.00</td>\n", "      <td>1459.87</td>\n", "      <td>1448.00</td>\n", "      <td>16391</td>\n", "      <td>2.380925e+09</td>\n", "      <td>0.81</td>\n", "      <td>-0.64</td>\n", "      <td>-9.31</td>\n", "      <td>0.13</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>128 rows × 13 columns</p>\n", "</div>"], "text/plain": ["     股票名称    股票代码                日期       开盘       收盘       最高       最低  \\\n", "0    贵州茅台  600519  2025-07-15 10:30  1420.98  1416.56  1422.93  1412.02   \n", "1    贵州茅台  600519  2025-07-15 11:30  1416.57  1410.00  1417.98  1409.88   \n", "2    贵州茅台  600519  2025-07-15 14:00  1409.99  1408.35  1410.96  1408.32   \n", "3    贵州茅台  600519  2025-07-15 15:00  1408.38  1411.00  1414.73  1408.37   \n", "4    贵州茅台  600519  2025-07-16 10:30  1410.01  1412.50  1417.49  1409.95   \n", "..    ...     ...               ...      ...      ...      ...      ...   \n", "123  贵州茅台  600519  2025-08-26 15:00  1488.00  1481.61  1488.19  1481.28   \n", "124  贵州茅台  600519  2025-08-27 10:30  1481.88  1463.10  1484.93  1462.00   \n", "125  贵州茅台  600519  2025-08-27 11:30  1463.09  1460.32  1465.33  1460.00   \n", "126  贵州茅台  600519  2025-08-27 14:00  1460.32  1457.31  1468.08  1456.74   \n", "127  贵州茅台  600519  2025-08-27 15:00  1457.99  1448.00  1459.87  1448.00   \n", "\n", "       成交量           成交额    振幅   涨跌幅    涨跌额   换手率  \n", "0    13377  1.896731e+09  0.77 -0.49  -7.04  0.11  \n", "1     8459  1.194497e+09  0.57 -0.46  -6.56  0.07  \n", "2     7031  9.909633e+08  0.19 -0.12  -1.65  0.06  \n", "3     6538  9.229586e+08  0.45  0.19   2.65  0.05  \n", "4     8441  1.193176e+09  0.53  0.11   1.50  0.07  \n", "..     ...           ...   ...   ...    ...   ...  \n", "123  10211  1.515541e+09  0.46 -0.46  -6.86  0.08  \n", "124  20519  3.013180e+09  1.55 -1.25 -18.51  0.16  \n", "125   9728  1.421546e+09  0.36 -0.19  -2.78  0.08  \n", "126   9368  1.368518e+09  0.78 -0.21  -3.01  0.07  \n", "127  16391  2.380925e+09  0.81 -0.64  -9.31  0.13  \n", "\n", "[128 rows x 13 columns]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import efinance as ef\n", "ef.stock.get_quote_history('600519', klt=60)"]}, {"cell_type": "code", "execution_count": null, "id": "a1558814-9f90-4738-9757-fb75f4d62319", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}