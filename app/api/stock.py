"""
股票相关API路由
"""
import logging
from typing import Optional
from fastapi import APIRouter, HTTPException, Query, Path
from app.models.response import BaseResponse, success_response, error_response
from app.models.stock import (
    StockBaseInfo, 
    StockKLineResponse, 
    KLineQueryParams
)
from app.services.stock_service import StockService
from app.core.exceptions import StockNotFoundError, DataFetchError, InvalidParameterError

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/v1/stocks", tags=["股票数据"])


@router.get(
    "/{stock_code}/base-info",
    response_model=BaseResponse[StockBaseInfo],
    summary="获取股票基本信息",
    description="根据股票代码获取股票的基本信息，包括市盈率、市净率、总股本等"
)
async def get_stock_base_info(
    stock_code: str = Path(..., description="股票代码", example="000001")
) -> BaseResponse[StockBaseInfo]:
    """获取股票基本信息"""
    try:
        logger.info(f"API请求: 获取股票基本信息 - {stock_code}")
        
        # 验证股票代码格式
        if not stock_code or len(stock_code) < 6:
            raise InvalidParameterError("股票代码格式不正确")
        
        # 调用服务获取数据
        stock_info = StockService.get_stock_base_info(stock_code)
        
        if not stock_info:
            raise StockNotFoundError(stock_code)
        
        return success_response(data=stock_info, message="获取股票基本信息成功")
        
    except (StockNotFoundError, InvalidParameterError) as e:
        logger.warning(f"客户端错误: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"获取股票基本信息失败: {stock_code}, 错误: {str(e)}")
        raise DataFetchError(str(e))


@router.get(
    "/{stock_code}/kline",
    response_model=BaseResponse[StockKLineResponse],
    summary="获取股票K线数据",
    description="根据股票代码获取历史K线数据，支持日K、周K、月K"
)
async def get_stock_kline_data(
    stock_code: str = Path(..., description="股票代码", example="000001"),
    period: str = Query("daily", description="周期类型", enum=["daily", "weekly", "monthly"]),
    start_date: Optional[str] = Query(None, description="开始日期(YYYYMMDD)", example="20240101"),
    end_date: Optional[str] = Query(None, description="结束日期(YYYYMMDD)", example="20241231"),
    count: Optional[int] = Query(100, description="数据条数", ge=1, le=1000)
) -> BaseResponse[StockKLineResponse]:
    """获取股票K线数据"""
    try:
        logger.info(f"API请求: 获取股票K线数据 - {stock_code}, 周期: {period}")
        
        # 验证股票代码格式
        if not stock_code or len(stock_code) < 6:
            raise InvalidParameterError("股票代码格式不正确")
        
        # 验证周期参数
        if period not in ["daily", "weekly", "monthly"]:
            raise InvalidParameterError("周期参数必须是 daily、weekly 或 monthly")
        
        # 验证日期格式
        if start_date and len(start_date) != 8:
            raise InvalidParameterError("开始日期格式不正确，应为YYYYMMDD")
        
        if end_date and len(end_date) != 8:
            raise InvalidParameterError("结束日期格式不正确，应为YYYYMMDD")
        
        # 调用服务获取数据
        kline_data = StockService.get_stock_kline_data(
            stock_code=stock_code,
            period=period,
            start_date=start_date,
            end_date=end_date,
            count=count
        )
        
        if not kline_data:
            raise StockNotFoundError(stock_code)
        
        return success_response(data=kline_data, message="获取股票K线数据成功")
        
    except (StockNotFoundError, InvalidParameterError) as e:
        logger.warning(f"客户端错误: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"获取股票K线数据失败: {stock_code}, 错误: {str(e)}")
        raise DataFetchError(str(e))


@router.get(
    "/health",
    summary="健康检查",
    description="检查股票数据服务是否正常运行"
)
async def health_check():
    """健康检查接口"""
    return success_response(data={"status": "healthy"}, message="股票数据服务运行正常")
