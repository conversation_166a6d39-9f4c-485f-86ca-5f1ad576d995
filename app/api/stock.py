"""
股票相关API路由
"""
import logging
from typing import Optional
from fastapi import APIRouter, HTTPException, Query, Path
from app.models.response import BaseResponse, success_response, error_response
from app.models.stock import (
    StockBaseInfo, 
    StockKLineResponse, 
    KLineQueryParams
)
from app.services.stock_service import StockService
from app.core.exceptions import StockNotFoundError, DataFetchError, InvalidParameterError

logger = logging.getLogger(__name__)
router = APIRouter(prefix="/api/stock", tags=["股票数据"])


@router.get(
    "/base-info/{stock_code}",
    response_model=BaseResponse[StockBaseInfo],
    summary="获取股票基本信息",
    description="根据股票代码获取股票的基本信息，包括市盈率、市净率、总股本等"
)
async def get_stock_base_info(
    stock_code: str = Path(..., description="股票代码", example="000001")
) -> BaseResponse[StockBaseInfo]:
    """获取股票基本信息"""
    try:
        logger.info(f"API请求: 获取股票基本信息 - {stock_code}")
        
        # 验证股票代码格式
        if not stock_code or len(stock_code) < 6:
            raise InvalidParameterError("股票代码格式不正确")
        
        # 调用服务获取数据
        stock_info = StockService.get_stock_base_info(stock_code)
        
        if not stock_info:
            raise StockNotFoundError(stock_code)
        
        return success_response(data=stock_info, message="获取股票基本信息成功")
        
    except (StockNotFoundError, InvalidParameterError) as e:
        logger.warning(f"客户端错误: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"获取股票基本信息失败: {stock_code}, 错误: {str(e)}")
        raise DataFetchError(str(e))


@router.get(
    "/kline/{stock_code}",
    response_model=BaseResponse[StockKLineResponse],
    summary="获取股票K线数据",
    description="根据股票代码获取历史K线数据，支持日K、周K、月K"
)
async def get_stock_kline_data(
    stock_code: str = Path(..., description="股票代码", example="000001"),
    period: int = Query(101, description="周期类型", enum=[101, 102, 103, 5, 30, 60]),
) -> BaseResponse[StockKLineResponse]:
    """获取股票K线数据"""
    try:
        logger.info(f"API请求: 获取股票K线数据 - {stock_code}, 周期: {period}")
        
        # 验证股票代码格式
        if not stock_code or len(stock_code) < 6:
            raise InvalidParameterError("股票代码格式不正确")
        
        # 验证周期参数
        if period not in [101, 102, 103, 5, 30, 60]:
            raise InvalidParameterError("周期参数必须是 101、102、103、5、30 或 60")
        
        # 调用服务获取数据
        kline_data = StockService.get_stock_kline_data(
            stock_code=stock_code,
            period=period,
        )
        
        if not kline_data:
            raise StockNotFoundError(stock_code)
        
        return success_response(data=kline_data, message="获取股票K线数据成功")
        
    except (StockNotFoundError, InvalidParameterError) as e:
        logger.warning(f"客户端错误: {str(e)}")
        raise e
    except Exception as e:
        logger.error(f"获取股票K线数据失败: {stock_code}, 错误: {str(e)}")
        raise DataFetchError(str(e))


@router.get(
    "/health",
    summary="健康检查",
    description="检查股票数据服务是否正常运行"
)
async def health_check():
    """健康检查接口"""
    return success_response(data={"status": "healthy"}, message="股票数据服务运行正常")
