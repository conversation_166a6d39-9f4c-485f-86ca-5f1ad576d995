"""
应用配置模块
"""
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """应用配置类"""
    
    # 应用基本信息
    app_name: str = "quantify-server"
    app_version: str = "1.0.0"
    debug: bool = False
    
    # 服务器配置
    host: str = "127.0.0.1"
    port: int = 8000
    
    # 日志配置
    log_level: str = "INFO"
    
    class Config:
        env_file = ".env"
        case_sensitive = False

    @property
    def app_title(self) -> str:
        """应用标题"""
        return f"{self.app_name} v{self.app_version}"

    @property
    def app_description(self) -> str:
        """应用描述"""
        return f"{self.app_name} - 数据接口服务"


# 全局配置实例
settings = Settings()
