"""
自定义异常类
"""
from fastapi import HTTPException
from typing import Any, Dict, Optional


class StockNotFoundError(HTTPException):
    """股票未找到异常"""
    def __init__(self, stock_code: str):
        super().__init__(
            status_code=404,
            detail=f"股票代码 {stock_code} 未找到"
        )


class DataFetchError(HTTPException):
    """数据获取异常"""
    def __init__(self, message: str):
        super().__init__(
            status_code=500,
            detail=f"数据获取失败: {message}"
        )


class InvalidParameterError(HTTPException):
    """参数错误异常"""
    def __init__(self, message: str):
        super().__init__(
            status_code=400,
            detail=f"参数错误: {message}"
        )
