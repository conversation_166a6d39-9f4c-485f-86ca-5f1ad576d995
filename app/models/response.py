"""
API响应模型
"""
from typing import Any, Optional, Generic, TypeVar
from pydantic import BaseModel

T = TypeVar('T')


class BaseResponse(BaseModel, Generic[T]):
    """基础响应模型"""
    success: bool = True
    message: str = "操作成功"
    data: Optional[T] = None
    code: int = 200


class ErrorResponse(BaseModel):
    """错误响应模型"""
    success: bool = False
    message: str
    code: int
    detail: Optional[str] = None


def success_response(data: Any = None, message: str = "操作成功") -> BaseResponse:
    """成功响应"""
    return BaseResponse(success=True, message=message, data=data, code=200)


def error_response(message: str, code: int = 400, detail: str = None) -> ErrorResponse:
    """错误响应"""
    return ErrorResponse(success=False, message=message, code=code, detail=detail)
