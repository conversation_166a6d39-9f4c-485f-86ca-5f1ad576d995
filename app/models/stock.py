"""
股票数据模型
"""
from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, Field


class StockBaseInfo(BaseModel):
    """股票基本信息模型"""
    stock_code: str = Field(..., description="股票代码")
    stock_name: str = Field(..., description="股票名称")
    market: Optional[str] = Field(None, description="市场")
    industry: Optional[str] = Field(None, description="行业")
    area: Optional[str] = Field(None, description="地区")
    pe: Optional[float] = Field(None, description="市盈率")
    outstanding: Optional[float] = Field(None, description="流通股本")
    totals: Optional[float] = Field(None, description="总股本")
    total_assets: Optional[float] = Field(None, description="总资产")
    liquid_assets: Optional[float] = Field(None, description="流动资产")
    fixed_assets: Optional[float] = Field(None, description="固定资产")
    reserved: Optional[float] = Field(None, description="公积金")
    reserved_per_share: Optional[float] = Field(None, description="每股公积金")
    esp: Optional[float] = Field(None, description="每股收益")
    bvps: Optional[float] = Field(None, description="每股净资产")
    pb: Optional[float] = Field(None, description="市净率")
    time_to_market: Optional[str] = Field(None, description="上市日期")
    undp: Optional[float] = Field(None, description="未分利润")
    per_undp: Optional[float] = Field(None, description="每股未分配")
    rev: Optional[float] = Field(None, description="收入同比")
    profit: Optional[float] = Field(None, description="利润同比")
    gpr: Optional[float] = Field(None, description="毛利率")
    npr: Optional[float] = Field(None, description="净利润率")
    holders: Optional[int] = Field(None, description="股东人数")


class KLineData(BaseModel):
    """K线数据模型"""
    date: str = Field(..., description="日期")
    open: float = Field(..., description="开盘价")
    close: float = Field(..., description="收盘价")
    high: float = Field(..., description="最高价")
    low: float = Field(..., description="最低价")
    volume: float = Field(..., description="成交量")
    amount: float = Field(..., description="成交额")
    amplitude: Optional[float] = Field(None, description="振幅")
    change_percent: Optional[float] = Field(None, description="涨跌幅")
    change_amount: Optional[float] = Field(None, description="涨跌额")
    turnover: Optional[float] = Field(None, description="换手率")


class StockKLineResponse(BaseModel):
    """股票K线数据响应模型"""
    stock_code: str = Field(..., description="股票代码")
    stock_name: str = Field(..., description="股票名称")
    period: str = Field(..., description="周期")
    data: List[KLineData] = Field(..., description="K线数据列表")


class StockQueryParams(BaseModel):
    """股票查询参数"""
    stock_code: str = Field(..., description="股票代码", example="000001")


class KLineQueryParams(BaseModel):
    """K线查询参数"""
    stock_code: str = Field(..., description="股票代码", example="000001")
    period: str = Field("daily", description="周期", example="daily")
    start_date: Optional[str] = Field(None, description="开始日期", example="20240101")
    end_date: Optional[str] = Field(None, description="结束日期", example="20241231")
    count: Optional[int] = Field(100, description="数据条数", example=100)
