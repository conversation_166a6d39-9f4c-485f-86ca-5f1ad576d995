"""
股票数据模型
"""
from typing import Optional, List
from datetime import datetime
from pydantic import BaseModel, Field


class StockBaseInfo(BaseModel):
    """股票基本信息模型"""
    code: str = Field(..., description="股票代码")
    name: str = Field(..., description="股票名称")
    pe: Optional[float] = Field(None, description="市盈率(动)")
    pb: Optional[float] = Field(None, description="市净率")
    industry: Optional[str] = Field(None, description="所处行业")
    mc: Optional[str] = Field(None, description="总市值")
    cmc: Optional[str] = Field(None, description="流通市值")
    block_id: Optional[str] = Field(None, description="板块编号")
    roe: Optional[float] = Field(None, description="净资产收益率")
    npr: Optional[float] = Field(None, description="净利率")
    np: Optional[float] = Field(None, description="净利润")
    gpr: Optional[float] = Field(None, description="毛利率")


class KLineData(BaseModel):
    """K线数据模型"""
    name: Optional[str] = Field(None, description="股票名称")
    code: Optional[str] = Field(None, description="股票代码")
    date: str = Field(..., description="日期")
    open: float = Field(..., description="开盘价")
    close: float = Field(..., description="收盘价")
    high: float = Field(..., description="最高价")
    low: float = Field(..., description="最低价")
    volume: float = Field(..., description="成交量")
    amount: float = Field(..., description="成交额")
    amplitude: Optional[float] = Field(None, description="振幅")
    change_percent: Optional[float] = Field(None, description="涨跌幅")
    change_amount: Optional[float] = Field(None, description="涨跌额")
    turnover: Optional[float] = Field(None, description="换手率")


class StockKLineResponse(BaseModel):
    """股票K线数据响应模型"""
    stock_code: str = Field(..., description="股票代码")
    stock_name: str = Field(..., description="股票名称")
    period: str = Field(..., description="周期")
    data: List[KLineData] = Field(..., description="K线数据列表")


class StockQueryParams(BaseModel):
    """股票查询参数"""
    code: str = Field(..., description="股票代码")


class KLineQueryParams(BaseModel):
    """K线查询参数"""
    code: str = Field(..., description="股票代码")
    period: int = Field(..., description="周期")
