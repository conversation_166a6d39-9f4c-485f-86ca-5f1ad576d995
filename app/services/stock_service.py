"""
股票数据服务
"""
import logging
from typing import List, Optional, Dict, Any
import efinance as ef
from app.models.stock import StockBaseInfo, KLineData, StockKLineResponse

logger = logging.getLogger(__name__)


class StockService:
    """股票数据服务类"""
    
    @staticmethod
    def get_stock_base_info(stock_code: str) -> Optional[StockBaseInfo]:
        """
        获取股票基本信息
        
        Args:
            stock_code: 股票代码
            
        Returns:
            StockBaseInfo: 股票基本信息对象
        """
        try:
            logger.info(f"获取股票基本信息: {stock_code}")
            
            # 调用efinance获取基本信息
            base_info = ef.stock.get_base_info(stock_code)
            
            if base_info is None or base_info.empty:
                logger.warning(f"未找到股票 {stock_code} 的基本信息")
                return None
            
            # 转换为我们的数据模型
            stock_info = StockBaseInfo(
                code=stock_code,
                name=base_info.get('股票名称', None),
                pe=base_info.get('市盈率(动)', None),
                pb=base_info.get('市净率', None),
                industry=base_info.get('行业', None),
                mc=base_info.get('总市值', None),
                cmc=base_info.get('流通市值', None),
                block_id=base_info.get('板块编号', None),
                roe=base_info.get('净资产收益率(%)', None),
                npr=base_info.get('净利润率(%)', None),
                np=base_info.get('每股收益', None),
                gpr=base_info.get('毛利率(%)', None)
            )
            
            logger.info(f"成功获取股票 {stock_code} 基本信息")
            return stock_info
            
        except Exception as e:
            logger.error(f"获取股票基本信息失败: {stock_code}, 错误: {str(e)}")
            raise Exception(f"获取股票基本信息失败: {str(e)}")
    
    @staticmethod
    def get_stock_kline_data(
        stock_code: str,
        period: int = 101,
    ) -> Optional[StockKLineResponse]:
        """
        获取股票历史K线数据
        
        Args:
            stock_code: 股票代码
            period: 周期 (101: 日K, 102: 周K, 103: 月K)
            
        Returns:
            StockKLineResponse: K线数据响应对象
        """
        try:
            logger.info(f"获取股票K线数据: {stock_code}, 周期: {period}")
            
            # 调用efinance获取历史数据
            kline_data = ef.stock.get_quote_history(
                stock_code,
                klt=period,
            )
            
            if kline_data is None or kline_data.empty:
                logger.warning(f"未找到股票 {stock_code} 的K线数据")
                return None
            
            # 转换为我们的数据模型
            kline_list = []
            for _, row in kline_data.iterrows():
                kline_item = KLineData(
                    name=row['股票名称'],
                    code=row['股票代码'],
                    date=row['日期'].strftime('%Y-%m-%d') if hasattr(row['日期'], 'strftime') else str(row['日期']),
                    open=float(row['开盘']),
                    close=float(row['收盘']),
                    high=float(row['最高']),
                    low=float(row['最低']),
                    volume=float(row['成交量']),
                    amount=float(row['成交额']),
                    amplitude=row.get('振幅', None),
                    change_percent=row.get('涨跌幅', None),
                    change_amount=row.get('涨跌额', None),
                    turnover=row.get('换手率', None)
                )
                kline_list.append(kline_item)
            
            # 获取股票名称
            stock_name = kline_data.iloc[0].get('股票名称', '') if not kline_data.empty else ''
            
            response = StockKLineResponse(
                code=stock_code,
                name=stock_name,
                period=str(period),
                data=kline_list
            )
            
            logger.info(f"成功获取股票 {stock_code} K线数据，共 {len(kline_list)} 条")
            return response
            
        except Exception as e:
            logger.error(f"获取股票K线数据失败: {stock_code}, 错误: {str(e)}")
            raise Exception(f"获取股票K线数据失败: {str(e)}")
    
    @staticmethod
    def _convert_period(period: str) -> int:
        """
        转换周期参数
        
        Args:
            period: 周期字符串
            
        Returns:
            int: efinance对应的周期参数
        """
        period_map = {
            "daily": 101,    # 日K
            "weekly": 102,   # 周K
            "monthly": 103   # 月K
        }
        return period_map.get(period.lower(), 101)
