"""
股票数据服务
"""
import logging
from typing import List, Optional, Dict, Any
import efinance as ef
from app.models.stock import StockBaseInfo, KLineData, StockKLineResponse

logger = logging.getLogger(__name__)


class StockService:
    """股票数据服务类"""
    
    @staticmethod
    def get_stock_base_info(stock_code: str) -> Optional[StockBaseInfo]:
        """
        获取股票基本信息
        
        Args:
            stock_code: 股票代码
            
        Returns:
            StockBaseInfo: 股票基本信息对象
        """
        try:
            logger.info(f"获取股票基本信息: {stock_code}")
            
            # 调用efinance获取基本信息
            base_info = ef.stock.get_base_info(stock_code)
            
            if not base_info:
                logger.warning(f"未找到股票 {stock_code} 的基本信息")
                return None
            
            # 转换为我们的数据模型
            stock_info = StockBaseInfo(
                stock_code=base_info.get('股票代码', stock_code),
                stock_name=base_info.get('股票名称', ''),
                market=base_info.get('市场', ''),
                industry=base_info.get('行业', ''),
                area=base_info.get('地区', ''),
                pe=base_info.get('市盈率', None),
                outstanding=base_info.get('流通股本', None),
                totals=base_info.get('总股本', None),
                total_assets=base_info.get('总资产', None),
                liquid_assets=base_info.get('流动资产', None),
                fixed_assets=base_info.get('固定资产', None),
                reserved=base_info.get('公积金', None),
                reserved_per_share=base_info.get('每股公积金', None),
                esp=base_info.get('每股收益', None),
                bvps=base_info.get('每股净资产', None),
                pb=base_info.get('市净率', None),
                time_to_market=base_info.get('上市日期', None),
                undp=base_info.get('未分利润', None),
                per_undp=base_info.get('每股未分配', None),
                rev=base_info.get('收入同比', None),
                profit=base_info.get('利润同比', None),
                gpr=base_info.get('毛利率', None),
                npr=base_info.get('净利润率', None),
                holders=base_info.get('股东人数', None)
            )
            
            logger.info(f"成功获取股票 {stock_code} 基本信息")
            return stock_info
            
        except Exception as e:
            logger.error(f"获取股票基本信息失败: {stock_code}, 错误: {str(e)}")
            raise Exception(f"获取股票基本信息失败: {str(e)}")
    
    @staticmethod
    def get_stock_kline_data(
        stock_code: str,
        period: str = "daily",
        start_date: Optional[str] = None,
        end_date: Optional[str] = None,
        count: Optional[int] = 100
    ) -> Optional[StockKLineResponse]:
        """
        获取股票历史K线数据
        
        Args:
            stock_code: 股票代码
            period: 周期 (daily, weekly, monthly)
            start_date: 开始日期 (YYYYMMDD)
            end_date: 结束日期 (YYYYMMDD)
            count: 数据条数
            
        Returns:
            StockKLineResponse: K线数据响应对象
        """
        try:
            logger.info(f"获取股票K线数据: {stock_code}, 周期: {period}")
            
            # 调用efinance获取历史数据
            kline_data = ef.stock.get_quote_history(
                stock_code,
                beg=start_date,
                end=end_date,
                klt=StockService._convert_period(period),
                fqt=1  # 前复权
            )
            
            if kline_data is None or kline_data.empty:
                logger.warning(f"未找到股票 {stock_code} 的K线数据")
                return None
            
            # 限制数据条数
            if count and len(kline_data) > count:
                kline_data = kline_data.tail(count)
            
            # 转换为我们的数据模型
            kline_list = []
            for _, row in kline_data.iterrows():
                kline_item = KLineData(
                    date=row['日期'].strftime('%Y-%m-%d') if hasattr(row['日期'], 'strftime') else str(row['日期']),
                    open=float(row['开盘']),
                    close=float(row['收盘']),
                    high=float(row['最高']),
                    low=float(row['最低']),
                    volume=float(row['成交量']),
                    amount=float(row['成交额']),
                    amplitude=row.get('振幅', None),
                    change_percent=row.get('涨跌幅', None),
                    change_amount=row.get('涨跌额', None),
                    turnover=row.get('换手率', None)
                )
                kline_list.append(kline_item)
            
            # 获取股票名称
            stock_name = kline_data.iloc[0].get('股票名称', '') if not kline_data.empty else ''
            
            response = StockKLineResponse(
                stock_code=stock_code,
                stock_name=stock_name,
                period=period,
                data=kline_list
            )
            
            logger.info(f"成功获取股票 {stock_code} K线数据，共 {len(kline_list)} 条")
            return response
            
        except Exception as e:
            logger.error(f"获取股票K线数据失败: {stock_code}, 错误: {str(e)}")
            raise Exception(f"获取股票K线数据失败: {str(e)}")
    
    @staticmethod
    def _convert_period(period: str) -> int:
        """
        转换周期参数
        
        Args:
            period: 周期字符串
            
        Returns:
            int: efinance对应的周期参数
        """
        period_map = {
            "daily": 101,    # 日K
            "weekly": 102,   # 周K
            "monthly": 103   # 月K
        }
        return period_map.get(period.lower(), 101)
