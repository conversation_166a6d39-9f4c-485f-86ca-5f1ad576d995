"""
股票数据服务
"""
import logging
from typing import Optional
import efinance as ef
from app.models.stock import StockBaseInfo, KLineData, StockKLineResponse

logger = logging.getLogger(__name__)


class StockService:
    """股票数据服务类"""

    @staticmethod
    def get_stock_base_info(stock_code: str) -> Optional[StockBaseInfo]:
        """
        获取股票基本信息

        Args:
            stock_code: 股票代码

        Returns:
            StockBaseInfo: 股票基本信息对象
        """
        try:
            logger.info(f"获取股票基本信息: {stock_code}")

            # 调用efinance获取基本信息
            base_info = ef.stock.get_base_info(stock_code)

            if base_info is None:
                logger.warning(f"未找到股票 {stock_code} 的基本信息")
                return None

            # base_info是DataFrame，取第一行数据
            info_row = base_info

            # 安全获取数据的辅助函数
            def safe_get_str(key: str) -> Optional[str]:
                value = info_row.get(key)
                return str(value) if value is not None and str(value) != 'nan' else None

            def safe_get_float(key: str) -> Optional[float]:
                value = info_row.get(key)
                if value is None or str(value) == 'nan':
                    return None
                try:
                    return float(value)
                except (ValueError, TypeError):
                    return None

            # 转换为我们的数据模型
            stock_info = StockBaseInfo(
                code=stock_code,
                name=safe_get_str('股票名称') or stock_code,  # 如果没有名称，使用代码
                pe=safe_get_float('市盈率(动)'),
                pb=safe_get_float('市净率'),
                industry=safe_get_str('c行业'),
                mc=safe_get_str('总市值'),
                cmc=safe_get_str('流通市值'),
                block_id=safe_get_str('板块编号'),
                roe=safe_get_float('净资产收益率(%)'),
                npr=safe_get_float('净利润率(%)'),
                np=safe_get_float('每股收益'),
                gpr=safe_get_float('毛利率(%)')
            )

            logger.info(f"成功获取股票 {stock_code} 基本信息")
            return stock_info

        except Exception as e:
            logger.error(f"获取股票基本信息失败: {stock_code}, 错误: {str(e)}")
            raise Exception(f"获取股票基本信息失败: {str(e)}")
    
    @staticmethod
    def get_stock_kline_data(
        stock_code: str,
        period: int = 101,
    ) -> Optional[StockKLineResponse]:
        """
        获取股票历史K线数据

        Args:
            stock_code: 股票代码
            period: 周期 (101: 日K, 102: 周K, 103: 月K)

        Returns:
            StockKLineResponse: K线数据响应对象
        """
        try:
            logger.info(f"获取股票K线数据: {stock_code}, 周期: {period}")

            # 调用efinance获取历史数据
            kline_data = ef.stock.get_quote_history(
                stock_code,
                klt=period,
            )

            # 检查返回的数据
            if kline_data is None:
                logger.warning(f"未找到股票 {stock_code} 的K线数据")
                return None

            # efinance可能返回DataFrame或dict，需要统一处理
            if isinstance(kline_data, dict):
                # 如果是字典，尝试获取主要的DataFrame
                if len(kline_data) == 0:
                    logger.warning(f"未找到股票 {stock_code} 的K线数据")
                    return None
                # 通常第一个值是主要数据
                kline_df = list(kline_data.values())[0]
            else:
                # 如果直接是DataFrame
                kline_df = kline_data

            # 检查DataFrame是否为空
            if kline_df is None or len(kline_df) == 0:
                logger.warning(f"未找到股票 {stock_code} 的K线数据")
                return None

            # 安全获取数值的辅助函数
            def safe_get_float(value) -> Optional[float]:
                if value is None or str(value) == 'nan':
                    return None
                try:
                    # Handle pandas Series by getting the first value
                    if hasattr(value, 'iloc'):
                        value = value.iloc[0]
                    return float(value)
                except (ValueError, TypeError, IndexError):
                    return None

            # 转换为我们的数据模型
            kline_list = []
            for _, row in kline_df.iterrows():
                kline_item = KLineData(
                    name=str(row.get('股票名称', '')) if row.get('股票名称') is not None else None,
                    code=str(row.get('股票代码', '')) if row.get('股票代码') is not None else None,
                    date=row['日期'].strftime('%Y-%m-%d') if hasattr(row['日期'], 'strftime') else str(row['日期']),
                    open=float(row['开盘']),
                    close=float(row['收盘']),
                    high=float(row['最高']),
                    low=float(row['最低']),
                    volume=float(row['成交量']),
                    amount=float(row['成交额']),
                    amplitude=safe_get_float(row.get('振幅')),
                    change_percent=safe_get_float(row.get('涨跌幅')),
                    change_amount=safe_get_float(row.get('涨跌额')),
                    turnover=safe_get_float(row.get('换手率'))
                )
                kline_list.append(kline_item)

            # 获取股票名称
            stock_name = str(kline_df.iloc[0].get('股票名称', '')) if len(kline_df) > 0 else ''

            response = StockKLineResponse(
                stock_code=stock_code,
                stock_name=stock_name,
                period=str(period),
                data=kline_list
            )

            logger.info(f"成功获取股票 {stock_code} K线数据，共 {len(kline_list)} 条")
            return response

        except Exception as e:
            logger.error(f"获取股票K线数据失败: {stock_code}, 错误: {str(e)}")
            raise Exception(f"获取股票K线数据失败: {str(e)}")
    
    @staticmethod
    def _convert_period(period: str) -> int:
        """
        转换周期参数
        
        Args:
            period: 周期字符串
            
        Returns:
            int: efinance对应的周期参数
        """
        period_map = {
            "daily": 101,    # 日K
            "weekly": 102,   # 周K
            "monthly": 103   # 月K
        }
        return period_map.get(period.lower(), 101)
