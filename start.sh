#!/bin/bash

# Quantify Server 启动脚本

echo "=== Quantify Server 启动脚本 ==="

# 检查conda环境
if ! command -v conda &> /dev/null; then
    echo "错误: 未找到conda命令，请确保已安装Anaconda或Miniconda"
    exit 1
fi

# 激活conda环境
echo "激活conda环境: quantify"
source $(conda info --base)/etc/profile.d/conda.sh
conda activate quantify

if [ $? -ne 0 ]; then
    echo "错误: 无法激活conda环境 'quantify'"
    echo "请确保已创建名为 'quantify' 的conda环境"
    exit 1
fi

# 检查Python版本
echo "检查Python版本..."
python --version

# 安装依赖
echo "安装Python依赖包..."
pip install -r requirements.txt

if [ $? -ne 0 ]; then
    echo "错误: 依赖包安装失败"
    exit 1
fi

# 启动服务
echo "启动Quantify Server..."
echo "服务将运行在: http://127.0.0.1:8000"
echo "API文档地址: http://127.0.0.1:8000/docs"
echo "按 Ctrl+C 停止服务"
echo ""

python main.py
